import 'package:json_annotation/json_annotation.dart';
import 'package:qiazhun/models/home_detail_model.dart';
import 'package:qiazhun/modules/account/account_model.dart';

part 'bookkeeping_model.g.dart';

@JsonSerializable()
class BookkeepingInfo extends Object {
  @Json<PERSON>ey(name: 'bookkeepingId')
  int? bookkeepingId;

  @Json<PERSON>ey(name: 'bookkeepingNumber')
  String? bookkeepingNumber;

  @JsonKey(name: 'accountBookName')
  String? accountBookName;

  @JsonKey(name: 'bookkeepingIcon')
  String? bookkeepingIcon;

  @JsonKey(name: 'memo')
  String? memo;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'isJoinTotal')
  String? isJoinTotal;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'billCount')
  int? billCount;

  BookkeepingInfo(this.bookkeepingId, this.bookkeepingNumber, this.accountBookName, this.bookkeepingIcon, this.isJoinTotal, this.memo, this.billCount);

  factory BookkeepingInfo.fromJson(Map<String, dynamic> srcJson) => _$BookkeepingInfoFromJson(srcJson);
}

@JsonSerializable()
class CategoryItem extends Object {
  @Json<PERSON>ey(name: 'bookkeepingCategoryId')
  int? bookkeepingCategoryId;

  @JsonKey(name: 'bookkeepingCategoryType')
  String? bookkeepingCategoryType;

  @JsonKey(name: 'bookkeepingCategoryLevel')
  String? bookkeepingCategoryLevel;

  @JsonKey(name: 'bookkeepingCategoryIcon')
  String? bookkeepingCategoryIcon;

  @JsonKey(name: 'bookkeepingCategoryName')
  String? bookkeepingCategoryName;

  @JsonKey(name: 'createTime')
  String? createTime;

  @JsonKey(name: 'threeCategoryList')
  List<CategoryItem>? threeCategoryList;
  @JsonKey(name: 'pid')
  num? pid;

  CategoryItem(this.bookkeepingCategoryId, this.bookkeepingCategoryType, this.bookkeepingCategoryLevel, this.bookkeepingCategoryIcon,
      this.bookkeepingCategoryName, this.createTime, this.pid, this.threeCategoryList);

  factory CategoryItem.fromJson(Map<String, dynamic> srcJson) => _$CategoryItemFromJson(srcJson);

  Map<String, dynamic> toJson() => _$CategoryItemToJson(this);
}

@JsonSerializable()
class BookkeepingCategoryResp {
  @JsonKey(name: 'expenses')
  List<CategoryItem>? expenses;
  @JsonKey(name: 'income')
  List<CategoryItem>? income;
  @JsonKey(name: 'officialExpenses')
  List<CategoryItem>? officialExpenses;
  @JsonKey(name: 'officialIncome')
  List<CategoryItem>? officialIncome;

  BookkeepingCategoryResp(this.expenses, this.income, this.officialExpenses, this.officialIncome);

  factory BookkeepingCategoryResp.fromJson(Map<String, dynamic> srcJson) => _$BookkeepingCategoryRespFromJson(srcJson);

  Map<String, dynamic> toJson() => _$BookkeepingCategoryRespToJson(this);
}

@JsonSerializable()
class BillDetailInfo extends Object {
  @JsonKey(name: 'bookkeepingId')
  int? bookkeepingId;

  @JsonKey(name: 'bookkeepingNumber')
  String? bookkeepingNumber;

  @JsonKey(name: 'bookkeepingName')
  String? bookkeepingName;

  @JsonKey(name: 'id')
  int? id;

  @JsonKey(name: 'icon')
  String? icon;

  @JsonKey(name: 'iconImage')
  String? iconImage;

  @JsonKey(name: 'categoryId')
  int? categoryId;

  @JsonKey(name: 'money')
  String? money;

  @JsonKey(name: 'after')
  String? after;

  @JsonKey(name: 'memo')
  String? memo;

  @JsonKey(name: 'type')
  String? type;

  @JsonKey(name: 'date')
  String? date;

  @JsonKey(name: 'week')
  String? week;

  @JsonKey(name: 'categoryName')
  String? categoryName;

  @JsonKey(name: 'accountName')
  String? accountName;

  @JsonKey(name: 'accountId')
  int? accountId;

  @JsonKey(name: 'accountType')
  String? accountType;

  @JsonKey(name: 'cardNo')
  String? cardNo;

  @JsonKey(name: 'isNecessaryStatus')
  String? isNecessaryStatus;

  @JsonKey(name: 'isSave')
  String? isSave;

  @JsonKey(name: 'isNecessary')
  String? isNecessary;

  @JsonKey(name: 'couponPrice')
  String? couponPrice;

  BillDetailInfo(
    this.bookkeepingId,
    this.bookkeepingNumber,
    this.bookkeepingName,
    this.id,
    this.icon,
    this.iconImage,
    this.categoryId,
    this.money,
    this.after,
    this.memo,
    this.type,
    this.date,
    this.week,
    this.categoryName,
    this.accountName,
    this.accountId,
    this.accountType,
    this.cardNo,
    this.isNecessaryStatus,
    this.isSave,
    this.isNecessary,
    this.couponPrice,
  );

  factory BillDetailInfo.fromJson(Map<String, dynamic> srcJson) => _$BillDetailInfoFromJson(srcJson);

  Map<String, dynamic> toJson() => _$BillDetailInfoToJson(this);
}

@JsonSerializable()
class BookkeepingDetailResp extends Object {
  @JsonKey(name: 'flowing_water')
  List<TransactionItem>? flowingWater;

  @JsonKey(name: 'income_total')
  String? incomeTotal;

  @JsonKey(name: 'expense_total')
  String? expenseTotal;

  @JsonKey(name: 'pie_chart_data')
  List<PieChartData>? pieChartData;

  @JsonKey(name: 'expense_by_category')
  List<ExpenseCategory>? expenseByCategory;

  BookkeepingDetailResp(
    this.flowingWater,
    this.incomeTotal,
    this.expenseTotal,
    this.pieChartData,
    this.expenseByCategory,
  );

  factory BookkeepingDetailResp.fromJson(Map<String, dynamic> srcJson) => _$BookkeepingDetailRespFromJson(srcJson);

  Map<String, dynamic> toJson() => _$BookkeepingDetailRespToJson(this);
}

@JsonSerializable()
class FlowingWater extends Object {
  @JsonKey(name: 'id')
  int? id;

  @JsonKey(name: 'moneyLogType')
  String? moneyLogType;

  @JsonKey(name: 'icon')
  String? icon;

  @JsonKey(name: 'iconImage')
  String? iconImage;

  @JsonKey(name: 'money')
  String? money;

  @JsonKey(name: 'couponPrice')
  String? couponPrice;

  @JsonKey(name: 'memo')
  String? memo;

  @JsonKey(name: 'categoryName')
  String? categoryName;

  @JsonKey(name: 'categoryId')
  int? categoryId;

  @JsonKey(name: 'accountType')
  String? accountType;

  @JsonKey(name: 'cardNo')
  String? cardNo;

  @JsonKey(name: 'accountName')
  String? accountName;

  @JsonKey(name: 'createTime')
  String? createTime;

  FlowingWater(
    this.id,
    this.moneyLogType,
    this.icon,
    this.iconImage,
    this.money,
    this.couponPrice,
    this.memo,
    this.categoryName,
    this.categoryId,
    this.accountType,
    this.cardNo,
    this.accountName,
    this.createTime,
  );

  factory FlowingWater.fromJson(Map<String, dynamic> srcJson) => _$FlowingWaterFromJson(srcJson);

  Map<String, dynamic> toJson() => _$FlowingWaterToJson(this);
}

@JsonSerializable()
class PieChartData extends Object {
  @JsonKey(name: 'category_id')
  dynamic categoryId;

  @JsonKey(name: 'category_name')
  String? categoryName;

  @JsonKey(name: 'amount')
  String? amount;

  @JsonKey(name: 'percentage')
  String? percentage;

  @JsonKey(name: 'count')
  String? count;

  @JsonKey(name: 'icon')
  String? icon;

  PieChartData(
    this.categoryId,
    this.categoryName,
    this.amount,
    this.percentage,
    this.count,
    this.icon,
  );

  factory PieChartData.fromJson(Map<String, dynamic> srcJson) => _$PieChartDataFromJson(srcJson);

  Map<String, dynamic> toJson() => _$PieChartDataToJson(this);
}

@JsonSerializable()
class FinancialDataResp {
  @JsonKey(name: 'categories')
  BookkeepingCategoryResp? categories;

  @JsonKey(name: 'bookkeepingList')
  List<BookkeepingInfo>? bookkeepingList;

  @JsonKey(name: 'accountList')
  List<AccountModel>? accountList;

  FinancialDataResp(this.categories, this.bookkeepingList, this.accountList);
  factory FinancialDataResp.fromJson(Map<String, dynamic> srcJson) => _$FinancialDataRespFromJson(srcJson);

  Map<String, dynamic> toJson() => _$FinancialDataRespToJson(this);
}

@JsonSerializable()
class ExpenseCategory extends Object {
  @JsonKey(name: 'name')
  String? name;
  @JsonKey(name: 'icon')
  String? icon;
  @JsonKey(name: 'amount')
  String? amount;
  @JsonKey(name: 'count')
  String? count;

  ExpenseCategory(
    this.name,
    this.icon,
    this.amount,
    this.count,
  );

  factory ExpenseCategory.fromJson(Map<String, dynamic> srcJson) => _$ExpenseCategoryFromJson(srcJson);

  Map<String, dynamic> toJson() => _$ExpenseCategoryToJson(this);
}
