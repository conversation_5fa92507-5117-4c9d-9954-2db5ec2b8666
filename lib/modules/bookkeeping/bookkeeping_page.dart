import 'package:flutter/material.dart';
import 'package:flutter/src/scheduler/ticker.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:math_expressions/math_expressions.dart';
import 'package:qiazhun/common/base_model.dart';
import 'package:qiazhun/common/loading.dart';
import 'package:qiazhun/common/utils.dart';
import 'package:qiazhun/constants/design.dart';
import 'package:qiazhun/modules/account/account_model.dart';
import 'package:qiazhun/modules/bookkeeping/bookkeeping_model.dart';
import 'package:qiazhun/modules/bookkeeping/bookkeeping_repo.dart';
import 'package:qiazhun/modules/bookkeeping/bookkeeping_store.dart';
import 'package:qiazhun/modules/mine_tab/user_store.dart';
import 'package:qiazhun/router/router.dart';
import 'package:qiazhun/tools/extensions.dart';
import 'package:qiazhun/tools/tools.dart';
import 'package:qiazhun/models/price_info.dart';
import 'package:qiazhun/widgets/price_view.dart';
import 'package:qiazhun/widgets/round_image.dart';

class BookkeepingPage extends StatefulWidget {
  final dynamic logId;
  final String? targetAccountId;
  const BookkeepingPage({super.key, this.logId, this.targetAccountId});

  @override
  State<StatefulWidget> createState() => _BookkeepingState();
}

class _CalcItemData {
  final int ratio;
  final String? icon;
  final double? iconWidth;
  final double? iconHeight;
  final Color bgColor;
  final Color borderColor;
  final double marginRight;
  final String? text;
  final double? fontSize;
  final Color? fontColor;
  final Widget? widget;
  final Function()? onTap;
  _CalcItemData(
      {required this.ratio,
      this.icon,
      this.iconWidth,
      this.iconHeight,
      required this.bgColor,
      required this.borderColor,
      required this.marginRight,
      this.text,
      this.fontSize,
      this.fontColor,
      this.widget,
      this.onTap});
}

class _CalcRowData {
  final double marginTop;
  final List<_CalcItemData> items;
  _CalcRowData({required this.marginTop, required this.items});
}

class _ShortcutItem {
  final String type;
  final String? icon;
  final Function()? onTap;
  _ShortcutItem({required this.type, this.icon, this.onTap});

  Widget buildItem() {
    return const SizedBox();
  }
}

class _BookkeepingState extends State<BookkeepingPage> {
  DateTime _selectedDay = DateTime.now();
  final DateFormat _dateFormat = DateFormat('yyyy-MM-dd');

  final TextEditingController _textController = TextEditingController();
  final FocusNode _textNode = FocusNode();

  //账户
  AccountModel? _selectedIncomeAccount;
  AccountModel? _selectedOutcomeAccount;
  //账本
  BookkeepingInfo? _bookkeepingInfo;
  //分类
  CategoryItem? _selectedOutcomeCategory;
  CategoryItem? _selectedIncomeCategory;

  int _selectedTab = 0;
  double? _bookkeepingResult;
  double? _savingResult;
  bool _noNeed = false;
  bool _isSavingInput = false;
  String _remarkStr = '';

  // 横滑组件的项目顺序
  List<_ShortcutItem> _horizontalItems = [
    _ShortcutItem(type: 'date'),
    _ShortcutItem(type: 'unnecessary'),
    _ShortcutItem(type: 'saving'),
    _ShortcutItem(type: 'accounts')
  ];

  void _showSettingsMenu() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => _SettingsMenuPanel(
        items: _horizontalItems,
        onReorder: (newOrder) {
          setState(() {
            _horizontalItems = newOrder;
          });
        },
      ),
    );
  }

  @override
  void initState() {
    super.initState();
    if (UserStore.to.lastIndexBookkeepingNumber?.isNotEmpty == true) {
      _bookkeepingInfo = BookkeepingStore.to.bookkeepingList.firstWhereOrNull((el) => el.bookkeepingNumber == UserStore.to.lastIndexBookkeepingNumber);
    }
    _loadData(requestDetail: widget.logId != null);
  }

  @override
  void dispose() {
    super.dispose();
    _textNode.dispose();
    _textController.dispose();
  }

  Future<void> _loadData({bool? requestDetail}) async {
    // Loading.show();
    List<Future<dynamic>> requests = [
      BookkeepingStore.to.getMyBookkeepingCategory(),
      BookkeepingStore.to.getBookkeepingList(),
      BookkeepingStore.to.getAccountList(),
    ];
    BillDetailInfo? detail;
    if (requestDetail == true) {
      requests.add(BookkeepingRepo.billDetail(widget.logId));
    }
    var resp = await Future.wait(requests);
    if (requestDetail == true) {
      BaseModel<BillDetailInfo> bm = resp[requests.length - 1] as BaseModel<BillDetailInfo>;
      if (bm.code == 1) {
        detail = bm.data;
      } else {
        showToast('获取流水信息失败');
      }
    }
    if (detail != null) {
      _bookkeepingInfo = BookkeepingStore.to.bookkeepingList.firstWhereOrNull((el) => el.bookkeepingNumber == detail!.bookkeepingNumber);
      _selectedTab = detail!.type == '2' ? 0 : 1;
      if (_selectedTab == 0) {
        _selectedOutcomeCategory = BookkeepingStore.to.outcomeCategories.firstWhereOrNull((el) => el.bookkeepingCategoryId == detail!.categoryId);
        _selectedOutcomeAccount = BookkeepingStore.to.accountList.firstWhereOrNull((el) {
          return el.id == detail!.accountId;
        });
      } else {
        _selectedIncomeCategory = BookkeepingStore.to.incomeCategories.firstWhereOrNull((el) => el.bookkeepingCategoryId == detail!.categoryId);
        _selectedIncomeAccount = BookkeepingStore.to.accountList.firstWhereOrNull((el) {
          return el.id == detail!.accountId;
        });
      }

      _selectedDay = _dateFormat.tryParse(detail.date ?? '') ?? DateTime.now();
      _bookkeepingResult = (NumberFormat().tryParse(detail?.money ?? '') ?? 0.0).toDouble();
      _textController.text = _bookkeepingResult?.toStringAsFixed(2) ?? '';
      _savingResult = _selectedTab == 0 ? (detail.couponPrice != null ? NumberFormat().tryParse(detail!.couponPrice!)?.toDouble() : null) : null;
      _remarkStr = detail.memo ?? '';
      _noNeed = detail.isNecessaryStatus == '2'; //_noNeed ? '2' : '1'
      _isSavingInput = false;
    }
    if (_bookkeepingInfo == null) {
      if (UserStore.to.lastIndexBookkeepingNumber?.isNotEmpty == true) {
        _bookkeepingInfo = BookkeepingStore.to.bookkeepingList.firstWhereOrNull((el) => el.bookkeepingNumber == UserStore.to.lastIndexBookkeepingNumber);
      }
    }
    _selectedOutcomeCategory ??= BookkeepingStore.to.outcomeCategories.isNotEmpty ? BookkeepingStore.to.outcomeCategories[0] : null;
    _selectedIncomeCategory ??= BookkeepingStore.to.incomeCategories.isNotEmpty ? BookkeepingStore.to.incomeCategories[0] : null;
    // if (_selectedOutcomeAccount == null) {
    //   for (var item in BookkeepingStore.to.accountList) {
    //     if (item.cardType != '5' && item.cardType != '6' && item.cardType != '7' && item.cardType != '8') {
    //       _selectedAccount = item;
    //       break;
    //     }
    //   }
    // }
    if (widget.targetAccountId != null) {
      _selectedIncomeAccount = BookkeepingStore.to.accountList.firstWhereOrNull((el) => '${el.id}' == widget.targetAccountId);
      _selectedOutcomeAccount = BookkeepingStore.to.accountList.firstWhereOrNull((el) => '${el.id}' == widget.targetAccountId);
    }
    _selectedIncomeAccount ??= BookkeepingStore.to.accountList.firstWhereOrNull((el) => el.lastSelectedIncome == 2);
    _selectedOutcomeAccount ??= BookkeepingStore.to.accountList.firstWhereOrNull((el) => el.lastSelectedPay == 2);

    // Loading.dismiss();
    setState(() {});
  }

  void _appendText(String append) {
    if (_textNode.hasFocus) {
      String preText = _textController.text;
      int start = _textController.selection.baseOffset;
      int end = _textController.selection.extentOffset;
      String newText = '';
      if (start != end) {
        newText = preText.replaceRange(start, end, append);
      } else {
        newText = preText.substring(0, start) + append + preText.substring(start, preText.length);
      }
      _textController.value = TextEditingValue(text: newText, selection: TextSelection(baseOffset: start + 1, extentOffset: start + 1));
      _textNode.unfocus();
    } else {
      _textController.text += append;
    }

    setState(() {});
  }

  void _deleteText() {
    String preText = _textController.text;
    String newText = '';
    if (_textNode.hasFocus) {
      int start = _textController.selection.baseOffset;
      int end = _textController.selection.extentOffset;
      if (start != end) {
        newText = preText.replaceRange(start, end, '');
      } else if (start == 0) {
        newText = preText;
      } else {
        newText = preText.substring(0, start - 1) + preText.substring(start, preText.length);
      }
    } else {
      if (preText.isNotEmpty) {
        newText = preText.substring(0, preText.length - 1);
      } else {
        newText = '';
      }
    }
    _textController.value = TextEditingValue(text: newText);
    _textNode.unfocus();

    setState(() {});
  }

  List<_CalcRowData> get _calWidgets {
    return [
      // 第一行：横滑组件（将在单独的widget中实现）
      // 第二行：运算符号
      _CalcRowData(marginTop: 0, items: [
        _CalcItemData(
          ratio: 1,
          icon: 'assets/images/ic_cal_div.png',
          iconWidth: 16,
          iconHeight: 16,
          bgColor: MColor.xFFFFFFFF,
          borderColor: MColor.skin,
          marginRight: 10,
          onTap: () {
            _appendText('÷');
          },
        ),
        _CalcItemData(
          ratio: 1,
          icon: 'assets/images/ic_cal_mul.png',
          iconWidth: 16,
          iconHeight: 16,
          bgColor: MColor.xFFFFFFFF,
          borderColor: MColor.skin,
          marginRight: 10,
          onTap: () {
            _appendText('×');
          },
        ),
        _CalcItemData(
            ratio: 1,
            icon: 'assets/images/ic_cal_sub.png',
            iconWidth: 16,
            iconHeight: 16,
            bgColor: MColor.xFFFFFFFF,
            borderColor: MColor.skin,
            marginRight: 10,
            onTap: () {
              _appendText('-');
            }),
        _CalcItemData(
            ratio: 1,
            icon: 'assets/images/ic_cal_add.png',
            iconWidth: 16,
            iconHeight: 16,
            bgColor: MColor.xFFFFFFFF,
            borderColor: MColor.skin,
            marginRight: 10,
            onTap: () {
              _appendText('+');
            }),
        _CalcItemData(
          ratio: 1,
          icon: 'assets/images/ic_cal_back.png',
          iconWidth: 27,
          iconHeight: 20,
          bgColor: MColor.xFFFFFFFF,
          borderColor: MColor.skin,
          marginRight: 0,
          onTap: () {
            _deleteText();
          },
        ),
      ]),
      // 第三行：1 2 3 4 再记
      _CalcRowData(marginTop: 10, items: [
        _CalcItemData(
          ratio: 1,
          text: '1',
          fontSize: 24,
          bgColor: MColor.xFFFFFFFF,
          borderColor: MColor.skin,
          marginRight: 10,
          onTap: () {
            _appendText('1');
          },
        ),
        _CalcItemData(
          ratio: 1,
          text: '2',
          fontSize: 24,
          bgColor: MColor.xFFFFFFFF,
          borderColor: MColor.skin,
          marginRight: 10,
          onTap: () {
            _appendText('2');
          },
        ),
        _CalcItemData(
          ratio: 1,
          text: '3',
          fontSize: 24,
          bgColor: MColor.xFFFFFFFF,
          borderColor: MColor.skin,
          marginRight: 10,
          onTap: () {
            _appendText('3');
          },
        ),
        _CalcItemData(
          ratio: 1,
          text: '4',
          fontSize: 24,
          bgColor: MColor.xFFFFFFFF,
          borderColor: MColor.skin,
          marginRight: 10,
          onTap: () {
            _appendText('4');
          },
        ),
        _CalcItemData(
          ratio: 1,
          text: '再记',
          fontSize: 14,
          bgColor: MColor.xFFFFFFFF,
          borderColor: MColor.skin,
          marginRight: 0,
          onTap: () {
            _confirm(false);
          },
        ),
      ]),
      // 第四行：5 6 7 8 账户
      _CalcRowData(marginTop: 10, items: [
        _CalcItemData(
          ratio: 1,
          text: '5',
          fontSize: 24,
          bgColor: MColor.xFFFFFFFF,
          borderColor: MColor.skin,
          marginRight: 10,
          onTap: () {
            _appendText('5');
          },
        ),
        _CalcItemData(
          ratio: 1,
          text: '6',
          fontSize: 24,
          bgColor: MColor.xFFFFFFFF,
          borderColor: MColor.skin,
          marginRight: 10,
          onTap: () {
            _appendText('6');
          },
        ),
        _CalcItemData(
          ratio: 1,
          text: '7',
          fontSize: 24,
          bgColor: MColor.xFFFFFFFF,
          borderColor: MColor.skin,
          marginRight: 10,
          onTap: () {
            _appendText('7');
          },
        ),
        _CalcItemData(
          ratio: 1,
          text: '8',
          fontSize: 24,
          bgColor: MColor.xFFFFFFFF,
          borderColor: MColor.skin,
          marginRight: 10,
          onTap: () {
            _appendText('8');
          },
        ),
        _CalcItemData(
          ratio: 1,
          text: _bookkeepingInfo == null ? '账本' : _bookkeepingInfo?.accountBookName ?? '',
          fontSize: 16,
          bgColor: MColor.xFFFFFFFF,
          borderColor: MColor.skin,
          marginRight: 0,
          onTap: () {
            RouterHelper.router.pushNamed(Routes.chooseLedgarPath, extra: {
              'selected': _bookkeepingInfo?.bookkeepingNumber?.isNotEmpty == true ? [_bookkeepingInfo!.bookkeepingNumber!] : <String>[],
              'multiSelect': false
            }).then((value) {
              if (value is Map && value.containsKey('selected') && value['selected'] is List && value['selected'].isNotEmpty == true) {
                _bookkeepingInfo = BookkeepingStore.to.bookkeepingList.firstWhereOrNull((el) => el.bookkeepingNumber == value['selected'][0]);
                setState(() {});
              }
            });
          },
        ),
      ]),
      // 第五行：设置 9 . 0 确认
      _CalcRowData(marginTop: 10, items: [
        _CalcItemData(
          ratio: 1,
          icon: 'assets/images/ic_setting.png',
          iconWidth: 20,
          iconHeight: 20,
          bgColor: MColor.xFFEEEEEE,
          borderColor: MColor.xFF999999,
          marginRight: 10,
          onTap: () {
            _showSettingsMenu();
          },
        ),
        _CalcItemData(
          ratio: 1,
          text: '9',
          fontSize: 24,
          bgColor: MColor.xFFFFFFFF,
          borderColor: MColor.skin,
          marginRight: 10,
          onTap: () {
            _appendText('9');
          },
        ),
        _CalcItemData(
          ratio: 1,
          text: '.',
          fontSize: 24,
          bgColor: MColor.xFFFFFFFF,
          borderColor: MColor.skin,
          marginRight: 10,
          onTap: () {
            _appendText('.');
          },
        ),
        _CalcItemData(
          ratio: 1,
          text: '0',
          fontSize: 24,
          bgColor: MColor.xFFFFFFFF,
          borderColor: MColor.skin,
          marginRight: 10,
          onTap: () {
            _appendText('0');
          },
        ),
        _CalcItemData(
          ratio: 1,
          text: '确认',
          fontSize: 16,
          fontColor: MColor.xFFFFFFFF,
          bgColor: MColor.xFFFF918D,
          borderColor: MColor.xFFED726E,
          marginRight: 0,
          onTap: () {
            _confirm(true);
          },
        ),
      ]),
    ];
  }

  void _confirm(bool needPop) {
    if (_textController.text.isNotEmpty) {
      var [isSuccess, result] = _calculate(_textController.text);
      if (isSuccess) {
        if (_isSavingInput) {
          _savingResult = result;
        } else {
          _bookkeepingResult = result;
        }
        setState(() {});
        _doRequest(needPop: needPop);
      }
    } else {
      _doRequest(needPop: needPop);
    }
  }

  List _calculate(String userInput) {
    try {
      String input = userInput.replaceAll('×', '*');
      input = input.replaceAll('÷', '/');
      Parser p = Parser();
      Expression expression = p.parse(input);
      ContextModel contextModel = ContextModel();

      double eval = expression.evaluate(EvaluationType.REAL, contextModel);
      return [true, eval];
    } catch (e) {
      showToast('输入错误');
      return [false, null];
    }
  }

  Future<void> _doRequest({bool? needPop = false}) async {
    if (_bookkeepingInfo?.bookkeepingNumber?.isNotEmpty != true) {
      showToast('请先选择账本');
      return;
    }
    if ((_selectedTab == 0 && _selectedOutcomeCategory?.bookkeepingCategoryId == null) ||
        (_selectedTab == 1 && _selectedIncomeCategory?.bookkeepingCategoryId == null)) {
      showToast('请先选择分类');
      return;
    }
    var selectedAccount = _selectedTab == 0 ? _selectedOutcomeAccount : _selectedIncomeAccount;
    if (selectedAccount?.id == null) {
      showToast('请先选择账户');
      return;
    }
    if (_bookkeepingResult == null) {
      showToast('请先输入金额');
      return;
    }
    if (_isSavingInput && _savingResult == null) {
      showToast('请先输入节省金额');
      return;
    }
    Loading.show();
    try {
      var resp = await BookkeepingRepo.addBill(
          bookkeepingNumber: _bookkeepingInfo!.bookkeepingNumber!,
          categoryId: _selectedTab == 0 ? _selectedOutcomeCategory!.bookkeepingCategoryId! : _selectedIncomeCategory!.bookkeepingCategoryId!,
          action: _selectedTab == 0 ? '2' : '1',
          money: _bookkeepingResult!.toStringAsFixed(2),
          isSave: (_savingResult != null && _savingResult! > 0) ? '2' : '1',
          isSaveMoney: (_savingResult != null && _savingResult! > 0) ? _savingResult?.toStringAsFixed(2) : null,
          isNecessaryStatus: _noNeed ? '2' : '1',
          nowTime: _dateFormat.format(_selectedDay),
          accountId: selectedAccount!.id!,
          memo: _remarkStr,
          moneyLogId: widget.logId);
      if (resp.code == 1) {
        showToast('添加流水成功');
        if (needPop == true) {
          RouterHelper.router.pop();
        } else {
          _textController.text = '';
          _isSavingInput = false;
          _bookkeepingResult = null;
          _savingResult = null;
          _noNeed = false;
          _remarkStr = '';
          setState(() {});
        }
      } else {
        showToast(resp.msg ?? '添加流水失败');
      }
    } catch (e, stacktrace) {
      debugPrintStack(stackTrace: stacktrace, maxFrames: 3);
      logger.e('addBill error $e');
      showToast('添加流水失败 $e');
    } finally {
      Loading.dismiss();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      extendBody: true,
      body: SafeArea(
        top: false,
        bottom: false,
        maintainBottomViewPadding: false,
        child: Container(
          child: Stack(
            children: [
              Container(
                color: Colors.transparent,
              ),
              Positioned(
                // top: 0,
                child: Container(
                  height: 203,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                        colors: [Color(0xFFF5F5F5), Color(0xFFAEE7CD)], begin: Alignment.bottomCenter, end: Alignment.topCenter, tileMode: TileMode.clamp),
                  ),
                ),
              ),
              Positioned(
                  child: Image.asset(
                    'assets/images/add_bill_bg.png',
                    fit: BoxFit.fitWidth,
                    width: MediaQuery.of(context).size.width,
                  ),
                  top: 0),
              Positioned(
                  top: 0,
                  left: 0,
                  right: 0,
                  child: AppBar(
                    leading: IconButton(
                      icon: Image.asset(
                        'assets/images/ic_back.png',
                        width: 24,
                        height: 24,
                      ),
                      onPressed: () {
                        RouterHelper.router.pop();
                      },
                    ),
                    backgroundColor: Colors.transparent,
                    scrolledUnderElevation: 0,
                    title: Image.asset(
                      'assets/images/icon_title.png',
                      width: 129,
                      height: 30,
                    ),
                    actions: [
                      GestureDetector(
                        onTap: () {
                          RouterHelper.router.pushNamed(Routes.chooseLedgarPath, extra: {
                            'selected': _bookkeepingInfo?.bookkeepingNumber?.isNotEmpty == true ? [_bookkeepingInfo!.bookkeepingNumber!] : <String>[],
                            'multiSelect': false
                          }).then((value) {
                            if (value is Map && value.containsKey('selected') && value['selected'] is List && value['selected'].isNotEmpty == true) {
                              _bookkeepingInfo = BookkeepingStore.to.bookkeepingList.firstWhereOrNull((el) => el.bookkeepingNumber == value['selected'][0]);
                              setState(() {});
                            }
                          });
                        },
                        child: Row(
                          children: [
                            Image.asset(
                              'assets/images/hand.png',
                              width: 24,
                              height: 24,
                            ),
                            const SizedBox(
                              width: 6,
                            ),
                            if (_bookkeepingInfo != null)
                              Text(
                                '${_bookkeepingInfo!.accountBookName}',
                                style: TextStyle(color: MColor.xFF1B1C1A, fontSize: 16, fontWeight: FontWeight.w500),
                              ),
                            const SizedBox(
                              width: 14,
                            )
                          ],
                        ),
                      )
                    ],
                  )),
              Positioned.fill(
                  top: AppBar().preferredSize.height + MediaQuery.of(context).padding.top,
                  // top: 0,
                  child: Builder(builder: (context) {
                    return _calculationView;
                  }))
            ],
          ),
        ),
      ),
    );
  }

  Widget get _calculationView {
    List<Widget> widgets = List.generate(_calWidgets.length, (index) {
      _CalcRowData rowData = _calWidgets[index];
      List<Widget> rowWidgets = [];
      List.generate(rowData.items.length, (rowIndex) {
        _CalcItemData itemData = rowData.items[rowIndex];
        rowWidgets.add(Expanded(
          flex: itemData.ratio,
          child: GestureDetector(
            onTap: () => itemData.onTap?.call(),
            child: Container(
              decoration: BoxDecoration(
                color: itemData.bgColor,
                borderRadius: BorderRadius.circular(10),
                border: Border.all(color: itemData.borderColor, width: 1),
                boxShadow: [
                  BoxShadow(
                    offset: Offset(0, 2),
                    blurRadius: 0,
                    color: itemData.borderColor,
                  ),
                ],
              ),
              height: 36,
              padding: EdgeInsets.symmetric(horizontal: 4),
              child: Center(
                child: itemData.widget != null
                    ? itemData.widget!
                    : itemData.icon != null
                        ? Image.asset(itemData.icon!, width: itemData.iconWidth, height: itemData.iconHeight, fit: BoxFit.fill)
                        : FittedBox(
                            fit: BoxFit.fill,
                            child: Text(itemData.text!,
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                                style: TextStyle(fontSize: itemData.fontSize!, height: 1, color: itemData.fontColor ?? itemData.borderColor)),
                          ),
              ),
            ),
          ),
        ));
        rowWidgets.add(SizedBox(
          width: itemData.marginRight,
        ));
      });

      return Column(
        children: [
          Container(
            margin: EdgeInsets.only(top: rowData.marginTop),
            child: Row(
              children: rowWidgets,
            ),
          ),
        ],
      );
    });
    return Column(
      children: [
        const Spacer(),
        Container(
          decoration: BoxDecoration(borderRadius: BorderRadius.vertical(top: Radius.circular(20)), color: MColor.xFFFFFFFF),
          child: Column(
            children: [
              const SizedBox(
                height: 14,
              ),
              Row(
                children: [
                  Expanded(
                      child: GestureDetector(
                    onTap: () {
                      _selectedTab = 0;
                      setState(() {});
                    },
                    child: Text(
                      '支出',
                      style: TextStyle(fontSize: 16, height: 1.4, color: _selectedTab == 0 ? MColor.skin : MColor.xFF999999),
                      textAlign: TextAlign.center,
                    ),
                  )),
                  Expanded(
                      child: GestureDetector(
                    onTap: () {
                      _selectedTab = 1;
                      _isSavingInput = false;
                      _savingResult = null;
                      _textController.text = _bookkeepingResult?.toStringAsFixed(2) ?? '';
                      setState(() {});
                    },
                    child: Text(
                      '收入',
                      style: TextStyle(fontSize: 16, height: 1.4, color: _selectedTab == 1 ? MColor.skin : MColor.xFF999999),
                      textAlign: TextAlign.center,
                    ),
                  )),
                  Expanded(
                      child: GestureDetector(
                    onTap: () {
                      RouterHelper.router.pushNamed(Routes.debtPath);
                    },
                    child: Text(
                      '借支',
                      style: TextStyle(fontSize: 16, height: 1.4, color: _selectedTab == 2 ? MColor.skin : MColor.xFF999999),
                      textAlign: TextAlign.center,
                    ),
                  )),
                ],
              ),
              Divider(
                height: 28,
                thickness: 0.5,
                color: MColor.skin.withOpacity(0.2),
                indent: 14,
              ),
              _tabContentView
            ],
          ),
        ),
        Container(
            padding: EdgeInsets.all(14),
            decoration: BoxDecoration(color: MColor.xFFD7F5E6),
            child: Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Container(
                          decoration: BoxDecoration(
                            color: Colors.yellow,
                            borderRadius: BorderRadius.circular(10),
                          ),
                          // padding: EdgeInsets.symmetric(horizontal: 14, vertical: 10),
                          height: 44,
                          // child: Text(
                          //   '123',
                          //   style: TextStyle(height: 1.1, fontSize: 20, color: MColor.xFF1B1C1A),
                          // ),
                          child: Builder(builder: (context) {
                            var prefix = '';
                            var label = '';
                            var labelColor;
                            if (_isSavingInput) {
                              label = _selectedTab == 0 ? '支出' : '收入';
                              prefix = '节省';
                              labelColor = MColor.skin;
                            } else {
                              label = _savingResult != null ? '节省' : '';
                              prefix = _selectedTab == 0 ? '支出' : '收入';
                              labelColor = MColor.xFFFFBE4A;
                            }
                            return TextField(
                              controller: _textController,
                              focusNode: _textNode,
                              keyboardType: TextInputType.none,
                              style: TextStyle(fontSize: 20, color: MColor.xFF1B1C1A, height: 1),
                              decoration: InputDecoration(
                                floatingLabelBehavior: FloatingLabelBehavior.always,
                                labelText: label,
                                labelStyle: TextStyle(fontSize: 14, color: labelColor, height: 1),
                                hintStyle: TextStyle(fontSize: 14, color: MColor.xFF999999, height: 1),
                                border: OutlineInputBorder(borderRadius: BorderRadius.circular(10), borderSide: BorderSide.none),
                                filled: true,
                                fillColor: MColor.xFFFFFFFF,
                                prefixIcon: Container(
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      const SizedBox(
                                        width: 10,
                                      ),
                                      if (_isSavingInput && _bookkeepingResult != null) ...[
                                        PriceView(
                                          price: PriceInfo.parsePrice(_bookkeepingResult!.toStringAsFixed(2)),
                                          integerFontSize: 16,
                                          fractionalFontSize: 14,
                                          textColor: MColor.xFFFFBE4A,
                                          prefix: prefix,
                                          prefixStyle: TextStyle(fontSize: 16, color: MColor.xFFFFBE4A, height: 1.4),
                                          showSymbol: false,
                                        ),
                                      ] else if (!_isSavingInput && _savingResult != null) ...[
                                        PriceView(
                                          price: PriceInfo.parsePrice(_savingResult!.toStringAsFixed(2)),
                                          integerFontSize: 16,
                                          fractionalFontSize: 14,
                                          textColor: MColor.skin,
                                          prefix: prefix,
                                          prefixStyle: TextStyle(fontSize: 16, color: MColor.skin, height: 1.4),
                                          showSymbol: false,
                                        ),
                                      ] else ...[
                                        Text(
                                          prefix,
                                          style: TextStyle(color: _isSavingInput ? MColor.xFFFFBE4A : MColor.skin, fontSize: 16, height: 1.4),
                                        ),
                                      ]
                                    ],
                                  ),
                                ),
                                suffixIcon: Container(
                                  // color: MColor.skin,
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      GestureDetector(
                                        onTap: () {
                                          showModalBottomSheet(
                                                  context: context,
                                                  builder: (context) => _RemarkPanel(
                                                        oldRemark: _remarkStr,
                                                      ),
                                                  /*  isScrollControlled: true, isScrollControlled: true, */ useRootNavigator: true)
                                              .then((value) {
                                            if (value != null) {
                                              _remarkStr = value as String;
                                            }
                                          });
                                        },
                                        child: RichText(
                                          textAlign: TextAlign.center,
                                          text: WidgetSpan(
                                              child: Row(
                                            mainAxisSize: MainAxisSize.min,
                                            children: [
                                              Text(
                                                '备注',
                                                style: TextStyle(color: _remarkStr.isNotEmpty ? MColor.skin : MColor.xFF999999, fontSize: 12, height: 1.4),
                                              ),
                                              const SizedBox(
                                                width: 2,
                                              ),
                                              Icon(
                                                Icons.edit_document,
                                                size: 14,
                                                color: _remarkStr.isNotEmpty ? MColor.skin : MColor.xFF999999,
                                              )
                                            ],
                                          )),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                // contentPadding: const EdgeInsets.symmetric(horizontal: 14, vertical: 0),
                                isDense: true,
                              ),
                              onChanged: (value) {
                                logger.i('BookkeepingPage onTextChanged $value');
                              },
                            );
                          })),
                    )
                  ],
                ),
                const SizedBox(
                  height: 14,
                ),
                // 横滑组件
                _buildHorizontalScrollWidget(),
                const SizedBox(
                  height: 14,
                ),
                ...widgets,
              ],
            )),
      ],
    );
  }

  Widget get _tabContentView {
    switch (_selectedTab) {
      case 0:
        _selectedOutcomeCategory ??= BookkeepingStore.to.outcomeCategories.isNotEmpty ? BookkeepingStore.to.outcomeCategories[0] : null;
        return _CategoryTab(
          0,
          BookkeepingStore.to.outcomeCategories,
          initialSelected: _selectedOutcomeCategory,
          onTap: (index) {
            setState(() {
              _selectedOutcomeCategory = index;
            });
          },
          onSettingTapped: () {
            RouterHelper.router.pushNamed(Routes.settingCategoryPath, extra: {'tab_id': 0}).then((_) {
              _loadData();
            });
          },
        );
      case 1:
        _selectedIncomeCategory ??= BookkeepingStore.to.incomeCategories.isNotEmpty ? BookkeepingStore.to.incomeCategories[0] : null;
        return _CategoryTab(
          1,
          BookkeepingStore.to.incomeCategories,
          initialSelected: _selectedIncomeCategory,
          onTap: (index) {
            setState(() {
              _selectedIncomeCategory = index;
            });
          },
          onSettingTapped: () {
            RouterHelper.router.pushNamed(Routes.settingCategoryPath, extra: {'tab_id': 1}).then((_) {
              _loadData();
            });
          },
        );
      default:
        return _CategoryTab(
          2,
          [],
          onTap: (index) {},
        );
    }
  }

  Widget _buildHorizontalScrollWidget() {
    return SizedBox(
      height: 28,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: _horizontalItems.length,
        itemBuilder: (context, index) {
          final itemType = _horizontalItems[index];
          return Container(
            margin: const EdgeInsets.only(right: 10),
            child: _buildHorizontalItem(itemType),
          );
        },
      ),
    );
  }

  Widget _buildHorizontalItem(_ShortcutItem itemType) {
    switch (itemType.type) {
      case 'date':
        return _buildDateItem();
      case 'unnecessary':
        return _buildUnnecessaryItem();
      case 'saving':
        return _buildSavingItem();
      case 'accounts':
        return _buildAccountsItem();
      default:
        return const SizedBox.shrink();
    }
  }

  Widget _buildDateItem() {
    return GestureDetector(
      onTap: () {
        showDatePicker(
          locale: const Locale('zh', 'CN'),
          context: context,
          firstDate: DateTime(2020, 1, 1),
          currentDate: _selectedDay,
          lastDate: DateTime.now().nextYear(),
        ).then((date) {
          if (date != null) {
            setState(() {
              _selectedDay = date;
            });
          }
        });
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 0),
        decoration: BoxDecoration(
          color: MColor.xFFFFF4E1,
          borderRadius: BorderRadius.circular(18),
          border: Border.all(color: MColor.xFFFFBE4A, width: 1),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Image.asset('assets/images/ic_cal_calendar.png', width: 16, height: 16),
            const SizedBox(width: 4),
            Text(
              _selectedDay.isSameDay(DateTime.now()) ? '今天' : DateFormat('M月d日').format(_selectedDay),
              style: const TextStyle(color: MColor.xFFFFBE4A, fontSize: 12),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildUnnecessaryItem() {
    return GestureDetector(
      onTap: () {
        setState(() {
          _noNeed = !_noNeed;
        });
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 0),
        decoration: BoxDecoration(
          color: _noNeed ? MColor.xFFFFFFFF : MColor.xFFEEEEEE,
          borderRadius: BorderRadius.circular(18),
          border: Border.all(color: _noNeed ? MColor.skin : MColor.xFF999999, width: 1),
        ),
        child: Row(
          children: [
            Text(
              '非必要',
              style: TextStyle(
                color: _noNeed ? MColor.skin : MColor.xFF999999,
                fontSize: 12,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSavingItem() {
    if (_selectedTab == 1) {
      return const SizedBox();
    }
    return GestureDetector(
      onTap: () {
        if (_selectedTab == 1) {
          showToast('收入不可设置节省');
          return;
        }
        _handleSavingTap();
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 0),
        decoration: BoxDecoration(
          color: _selectedTab == 0 ? MColor.xFFFFF4E1 : MColor.xFFEEEEEE,
          borderRadius: BorderRadius.circular(18),
          border: Border.all(
            color: _selectedTab == 0 ? MColor.xFFFFBE4A : MColor.xFF999999,
            width: 1,
          ),
        ),
        child: Row(
          children: [
            Text(
              '节省',
              style: TextStyle(
                color: _selectedTab == 0 ? MColor.xFFFFBE4A : MColor.xFF999999,
                fontSize: 12,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAccountsItem() {
    final selectedAccount = _selectedTab == 0 ? _selectedOutcomeAccount : _selectedIncomeAccount;

    return GestureDetector(
      onTap: () {
        RouterHelper.router.pushNamed(
          Routes.accountListPath,
          extra: {
            'selectMode': true,
            'limitAccount': _selectedTab == 0 ? '5' : '4',
          },
        ).then((value) {
          if (value != null && value is Map<String, dynamic> && value.containsKey('selected')) {
            if (_selectedTab == 0) {
              _selectedOutcomeAccount = value['selected'] as AccountModel;
            } else {
              _selectedIncomeAccount = value['selected'] as AccountModel;
            }
            setState(() {});
          }
        });
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 0),
        decoration: BoxDecoration(
          color: MColor.xFFFFFFFF,
          borderRadius: BorderRadius.circular(18),
          border: Border.all(color: MColor.skin, width: 1),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Image.asset('assets/images/ic_card.png', width: 16, height: 16),
            const SizedBox(width: 4),
            Text(
              selectedAccount?.accountName ?? '账户',
              style: const TextStyle(color: MColor.skin, fontSize: 12),
            ),
          ],
        ),
      ),
    );
  }

  void _handleSavingTap() {
    if (_isSavingInput) {
      if (_textController.text.isNotEmpty) {
        var [isSuccess, result] = _calculate(_textController.text);
        if (isSuccess) {
          _isSavingInput = false;
          _savingResult = result;
          _textController.text = _bookkeepingResult!.toStringAsFixed(2);
          setState(() {});
        }
      } else {
        _isSavingInput = false;
        _savingResult = null;
        _textController.text = _bookkeepingResult!.toStringAsFixed(2);
        setState(() {});
      }
    } else {
      if (_textController.text.isNotEmpty) {
        var [isSuccess, result] = _calculate(_textController.text);
        if (isSuccess) {
          _isSavingInput = true;
          _bookkeepingResult = result;
          _textController.text = _savingResult?.toStringAsFixed(2) ?? '';
          setState(() {});
        }
      } else {
        showToast('请先输入${_selectedTab == 0 ? '支出' : '收入'}金额，再输入节省金额');
      }
    }
  }
}

class _CategoryTab extends StatefulWidget {
  final int categoryTabId;
  final List<CategoryItem> categories;
  final CategoryItem? initialSelected;
  final Function(CategoryItem? categoryItem)? onTap;
  final Function()? onSettingTapped;
  const _CategoryTab(this.categoryTabId, this.categories, {this.initialSelected, this.onTap, this.onSettingTapped});
  @override
  State<StatefulWidget> createState() => _CategoryTabState();
}

class _CategoryTabState extends State<_CategoryTab> {
  CategoryItem? _selected;
  final PageController _pageController = PageController();
  int _currentPage = 0;

  @override
  void initState() {
    super.initState();
    _selected = widget.initialSelected;
  }

  @override
  void didUpdateWidget(covariant _CategoryTab oldWidget) {
    _selected = widget.initialSelected;
    _currentPage = 0;
    super.didUpdateWidget(oldWidget);
  }

  @override
  Widget build(BuildContext context) {
    debugPrint('_CategoryTabState ${widget.categoryTabId} currentPage=${_currentPage}');
    List<List<Widget>> pages = [];
    List<Widget> currentPage = [];

    double space = (MediaQuery.of(context).size.width - 5 * 70 - 20) / 4;

    bool settingAdded = false;
    // 添加分类按钮
    for (int i = 0; i < widget.categories.length; i++) {
      currentPage.add(_itemView(i, widget.categories[i]));

      // 每8个分类作为一页
      if (currentPage.length == 10 || i == widget.categories.length - 1) {
        // 如果是最后一页，添加设置按钮
        if (i == widget.categories.length - 1) {
          if (currentPage.length < 10) {
            currentPage.add(_settingItemView(widget.categoryTabId));
            settingAdded = true;
          } else {
            pages.add([...currentPage]);
            currentPage = [_settingItemView(widget.categoryTabId)];
            settingAdded = true;
          }
        }
        pages.add([...currentPage]);
        currentPage = [];
      }
    }

    // 如果最后一页没有设置按钮，添加一个新页面放置设置按钮
    if (!settingAdded) {
      pages.add([_settingItemView(widget.categoryTabId)]);
    }

    return Container(
      height: 145,
      child: Column(
        children: [
          Expanded(
            child: Stack(
              children: [
                PageView.builder(
                  controller: _pageController,
                  onPageChanged: (page) {
                    setState(() {
                      _currentPage = page;
                    });
                  },
                  itemCount: pages.length,
                  itemBuilder: (context, pageIndex) {
                    return Container(
                      margin: EdgeInsets.symmetric(horizontal: 10),
                      child: Wrap(
                        spacing: space,
                        runSpacing: 4,
                        alignment: WrapAlignment.start,
                        children: pages[pageIndex].map((widget) {
                          return SizedBox(
                            width: 70,
                            child: widget,
                          );
                        }).toList(),
                      ),
                    );
                  },
                ),
                if (pages.length > 1)
                  Positioned(
                    bottom: 0,
                    left: 0,
                    right: 0,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: List.generate(
                        pages.length,
                        (index) => Container(
                          margin: EdgeInsets.symmetric(horizontal: 4),
                          width: 6,
                          height: 6,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: _currentPage == index ? MColor.skin : MColor.xFFEBEBEB,
                          ),
                        ),
                      ),
                    ),
                  ),
              ],
            ),
          ),
          const SizedBox(height: 7),
        ],
      ),
    );
  }

  Widget _settingItemView(int tabId) {
    return GestureDetector(
      onTap: () {
        // _selected = index;
        // setState(() {});
        widget.onSettingTapped?.call();
      },
      child: Container(
        child: Column(
          children: [
            Image.asset('assets/images/ic_setting.png', width: 38, height: 38),
            // RoundImage(imageUrl: getImageUrl(item.bookkeepingCategoryIcon ?? ''), radius: 17, size: 34),
            const SizedBox(
              height: 3,
            ),
            Text(
              '设置',
              style: TextStyle(height: 1.4, fontSize: 12, color: MColor.xFF1B1C1A),
            ),
            const SizedBox(
              height: 8,
            )
          ],
        ),
      ),
    );
  }

  Widget _itemView(int index, CategoryItem item) {
    return GestureDetector(
      onTap: () {
        if (_selected == item) {
          _selected = null;
        } else {
          _selected = item;
        }
        widget.onTap?.call(_selected);

        setState(() {});
      },
      child: Container(
        width: 62,
        child: Column(
          children: [
            Container(
                decoration: BoxDecoration(
                    border: Border.all(color: _selected?.bookkeepingCategoryId == item.bookkeepingCategoryId ? MColor.skin : Colors.transparent, width: 2),
                    borderRadius: BorderRadius.circular(19),
                    color: MColor.xFFECECEC),
                child: RoundImage(imageUrl: getImageUrl(item.bookkeepingCategoryIcon ?? ''), radius: 17, size: 34)),
            const SizedBox(
              height: 3,
            ),
            FittedBox(
              fit: BoxFit.scaleDown,
              child: Text(
                item.bookkeepingCategoryName ?? '',
                maxLines: 1,
                style: TextStyle(
                    height: 1.4, fontSize: 12, color: _selected?.bookkeepingCategoryId == item.bookkeepingCategoryId ? MColor.skin : MColor.xFF1B1C1A),
              ),
            ),
            const SizedBox(
              height: 8,
            )
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }
}

class _RemarkPanel extends StatefulWidget {
  final String oldRemark;

  const _RemarkPanel({required this.oldRemark});

  @override
  State<StatefulWidget> createState() => _RemarkState();
}

class _RemarkState extends State<_RemarkPanel> {
  final TextEditingController _remarkController = TextEditingController();
  final FocusNode _remarkNode = FocusNode();

  @override
  void initState() {
    super.initState();
    _remarkController.text = widget.oldRemark;
  }

  @override
  void dispose() {
    _remarkController.dispose();
    _remarkNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Theme(
        data: Theme.of(context).copyWith(
          dividerTheme: DividerThemeData(
            color: Colors.transparent,
          ),
        ),
        child: ClipRRect(
            borderRadius: BorderRadius.vertical(top: Radius.circular(28)),
            child: Scaffold(
                backgroundColor: MColor.xFFF5F5F5,
                appBar: AppBar(
                  backgroundColor: MColor.xFFF5F5F5,
                  title: Text('填写备注', style: TextStyle(height: 1.4, fontSize: 16, color: MColor.xFF1B1C1A)),
                  scrolledUnderElevation: 0,
                  centerTitle: true,
                  automaticallyImplyLeading: false,
                  actions: [
                    IconButton(
                        onPressed: () {
                          RouterHelper.router.pop();
                        },
                        icon: Icon(Icons.close, size: 22, color: MColor.xFF999999))
                  ],
                ),
                persistentFooterButtons: [
                  GestureDetector(
                    onTap: () {
                      RouterHelper.router.pop(_remarkController.text);
                    },
                    child: Container(
                      height: 50,
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(25),
                          gradient: LinearGradient(
                              begin: const FractionalOffset(0.0, 0.0), end: const FractionalOffset(0.0, 1.0), colors: [Color(0xFF68C2BF), Color(0xFF4C9B93)])),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            '确认',
                            style: TextStyle(color: MColor.xFFFFFFFF, fontWeight: FontWeight.w500, fontSize: 16, height: 1),
                          ),
                        ],
                      ),
                    ),
                  )
                ],
                body: SafeArea(
                    child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 14.0),
                  child: TextField(
                    autofocus: true,
                    controller: _remarkController,
                    focusNode: _remarkNode,
                    textInputAction: TextInputAction.done,
                    onSubmitted: (value) {
                      RouterHelper.router.pop(_remarkController.text);
                    },
                    style: TextStyle(fontSize: 14, color: MColor.xFF1B1C1A, height: 1),
                    maxLength: 100,
                    decoration: InputDecoration(
                      hintText: '请填写备注',
                      hintStyle: TextStyle(fontSize: 14, color: MColor.xFF999999, height: 1),
                      border: OutlineInputBorder(borderRadius: BorderRadius.circular(10), borderSide: BorderSide.none),
                      filled: true,
                      fillColor: MColor.xFFFFFFFF,

                      // contentPadding: const EdgeInsets.symmetric(horizontal: 14, vertical: 0),
                      isDense: true,
                    ),
                    onChanged: (value) {
                      logger.i('BookkeepingPage onTextChanged $value');
                    },
                  ),
                )))));
  }
}

class _SettingsMenuPanel extends StatefulWidget {
  final List<_ShortcutItem> items;
  final Function(List<_ShortcutItem>) onReorder;

  const _SettingsMenuPanel({
    required this.items,
    required this.onReorder,
  });

  @override
  State<_SettingsMenuPanel> createState() => _SettingsMenuPanelState();
}

class _SettingsMenuPanelState extends State<_SettingsMenuPanel> {
  late List<_ShortcutItem> _items;

  @override
  void initState() {
    super.initState();
    _items = List.from(widget.items);
  }

  String _getItemName(_ShortcutItem item) {
    switch (item.type) {
      case 'date':
        return '日期选择';
      case 'unnecessary':
        return '非必要';
      case 'saving':
        return '节省';
      case 'accounts':
        return '账户';
      default:
        return item.type;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.6,
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          // 标题栏
          Container(
            padding: const EdgeInsets.all(16),
            decoration: const BoxDecoration(
              border: Border(bottom: BorderSide(color: Color(0xFFF5F5F5))),
            ),
            child: Row(
              children: [
                GestureDetector(
                  onTap: () => Navigator.pop(context),
                  child: const Icon(Icons.close, color: MColor.xFF999999),
                ),
                const Expanded(
                  child: Text(
                    '设置横滑组件',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: MColor.xFF1B1C1A,
                    ),
                  ),
                ),
                GestureDetector(
                  onTap: () {
                    widget.onReorder(_items);
                    Navigator.pop(context);
                  },
                  child: const Text(
                    '完成',
                    style: TextStyle(
                      fontSize: 16,
                      color: MColor.skin,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),
          // 提示文字
          const Padding(
            padding: EdgeInsets.all(16),
            child: Text(
              '长按拖动可以调整顺序',
              style: TextStyle(
                fontSize: 14,
                color: MColor.xFF999999,
              ),
            ),
          ),
          // 可拖动列表
          Expanded(
            child: ReorderableListView.builder(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              itemCount: _items.length,
              onReorder: (oldIndex, newIndex) {
                setState(() {
                  if (newIndex > oldIndex) {
                    newIndex -= 1;
                  }
                  final item = _items.removeAt(oldIndex);
                  _items.insert(newIndex, item);
                });
              },
              itemBuilder: (context, index) {
                final item = _items[index];
                return Container(
                  key: ValueKey(item),
                  margin: const EdgeInsets.only(bottom: 8),
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: const Color(0xFFF8F8F8),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        _getItemIcon(item),
                        color: MColor.skin,
                        size: 20,
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          _getItemName(item),
                          style: const TextStyle(
                            fontSize: 16,
                            color: MColor.xFF1B1C1A,
                          ),
                        ),
                      ),
                      const Icon(
                        Icons.drag_handle,
                        color: MColor.xFF999999,
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  IconData _getItemIcon(_ShortcutItem item) {
    switch (item.type) {
      case 'date':
        return Icons.calendar_today;
      case 'unnecessary':
        return Icons.remove_circle_outline;
      case 'saving':
        return Icons.savings_outlined;
      case 'accounts':
        return Icons.account_balance_wallet_outlined;
      default:
        return Icons.help_outline;
    }
  }
}
