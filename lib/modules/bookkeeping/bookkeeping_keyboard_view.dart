import 'package:flutter/material.dart';
import 'package:math_expressions/math_expressions.dart';
import 'package:qiazhun/constants/design.dart';
import 'package:qiazhun/tools/tools.dart';

class BookkeepingKeyboardView extends StatelessWidget {
  final Function(String) onAppendText;
  final Function() onDeleteText;
  final Function(bool) onConfirm;
  final String? bookkeepingName;
  final Function()? onBookkeepingTap;
  final Function()? onSettingsTap;

  const BookkeepingKeyboardView({
    super.key,
    required this.onAppendText,
    required this.onDeleteText,
    required this.onConfirm,
    this.bookkeepingName,
    this.onBookkeepingTap,
    this.onSettingsTap,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: _buildKeyboardRows(),
    );
  }

  List<Widget> _buildKeyboardRows() {
    return [
      // 第一行：运算符号
      _buildKeyboardRow([
        _KeyboardItem(
          ratio: 1,
          icon: 'assets/images/ic_cal_div.png',
          iconWidth: 16,
          iconHeight: 16,
          bgColor: MColor.xFFFFFFFF,
          borderColor: MColor.skin,
          marginRight: 10,
          onTap: () => onAppendText('÷'),
        ),
        _KeyboardItem(
          ratio: 1,
          icon: 'assets/images/ic_cal_mul.png',
          iconWidth: 16,
          iconHeight: 16,
          bgColor: MColor.xFFFFFFFF,
          borderColor: MColor.skin,
          marginRight: 10,
          onTap: () => onAppendText('×'),
        ),
        _KeyboardItem(
          ratio: 1,
          icon: 'assets/images/ic_cal_sub.png',
          iconWidth: 16,
          iconHeight: 16,
          bgColor: MColor.xFFFFFFFF,
          borderColor: MColor.skin,
          marginRight: 10,
          onTap: () => onAppendText('-'),
        ),
        _KeyboardItem(
          ratio: 1,
          icon: 'assets/images/ic_cal_add.png',
          iconWidth: 16,
          iconHeight: 16,
          bgColor: MColor.xFFFFFFFF,
          borderColor: MColor.skin,
          marginRight: 10,
          onTap: () => onAppendText('+'),
        ),
        _KeyboardItem(
          ratio: 1,
          icon: 'assets/images/ic_cal_back.png',
          iconWidth: 27,
          iconHeight: 20,
          bgColor: MColor.xFFFFFFFF,
          borderColor: MColor.skin,
          marginRight: 0,
          onTap: onDeleteText,
        ),
      ], marginTop: 0),
      
      // 第二行：1 2 3 4 再记
      _buildKeyboardRow([
        _KeyboardItem(
          ratio: 1,
          text: '1',
          fontSize: 24,
          bgColor: MColor.xFFFFFFFF,
          borderColor: MColor.skin,
          marginRight: 10,
          onTap: () => onAppendText('1'),
        ),
        _KeyboardItem(
          ratio: 1,
          text: '2',
          fontSize: 24,
          bgColor: MColor.xFFFFFFFF,
          borderColor: MColor.skin,
          marginRight: 10,
          onTap: () => onAppendText('2'),
        ),
        _KeyboardItem(
          ratio: 1,
          text: '3',
          fontSize: 24,
          bgColor: MColor.xFFFFFFFF,
          borderColor: MColor.skin,
          marginRight: 10,
          onTap: () => onAppendText('3'),
        ),
        _KeyboardItem(
          ratio: 1,
          text: '4',
          fontSize: 24,
          bgColor: MColor.xFFFFFFFF,
          borderColor: MColor.skin,
          marginRight: 10,
          onTap: () => onAppendText('4'),
        ),
        _KeyboardItem(
          ratio: 1,
          text: '再记',
          fontSize: 14,
          bgColor: MColor.xFFFFFFFF,
          borderColor: MColor.skin,
          marginRight: 0,
          onTap: () => onConfirm(false),
        ),
      ], marginTop: 10),
      
      // 第三行：5 6 7 8 账户
      _buildKeyboardRow([
        _KeyboardItem(
          ratio: 1,
          text: '5',
          fontSize: 24,
          bgColor: MColor.xFFFFFFFF,
          borderColor: MColor.skin,
          marginRight: 10,
          onTap: () => onAppendText('5'),
        ),
        _KeyboardItem(
          ratio: 1,
          text: '6',
          fontSize: 24,
          bgColor: MColor.xFFFFFFFF,
          borderColor: MColor.skin,
          marginRight: 10,
          onTap: () => onAppendText('6'),
        ),
        _KeyboardItem(
          ratio: 1,
          text: '7',
          fontSize: 24,
          bgColor: MColor.xFFFFFFFF,
          borderColor: MColor.skin,
          marginRight: 10,
          onTap: () => onAppendText('7'),
        ),
        _KeyboardItem(
          ratio: 1,
          text: '8',
          fontSize: 24,
          bgColor: MColor.xFFFFFFFF,
          borderColor: MColor.skin,
          marginRight: 10,
          onTap: () => onAppendText('8'),
        ),
        _KeyboardItem(
          ratio: 1,
          text: bookkeepingName ?? '账本',
          fontSize: 16,
          bgColor: MColor.xFFFFFFFF,
          borderColor: MColor.skin,
          marginRight: 0,
          onTap: onBookkeepingTap,
        ),
      ], marginTop: 10),
      
      // 第四行：设置 9 . 0 确认
      _buildKeyboardRow([
        _KeyboardItem(
          ratio: 1,
          icon: 'assets/images/ic_setting.png',
          iconWidth: 20,
          iconHeight: 20,
          bgColor: MColor.xFFEEEEEE,
          borderColor: MColor.xFF999999,
          marginRight: 10,
          onTap: onSettingsTap,
        ),
        _KeyboardItem(
          ratio: 1,
          text: '9',
          fontSize: 24,
          bgColor: MColor.xFFFFFFFF,
          borderColor: MColor.skin,
          marginRight: 10,
          onTap: () => onAppendText('9'),
        ),
        _KeyboardItem(
          ratio: 1,
          text: '.',
          fontSize: 24,
          bgColor: MColor.xFFFFFFFF,
          borderColor: MColor.skin,
          marginRight: 10,
          onTap: () => onAppendText('.'),
        ),
        _KeyboardItem(
          ratio: 1,
          text: '0',
          fontSize: 24,
          bgColor: MColor.xFFFFFFFF,
          borderColor: MColor.skin,
          marginRight: 10,
          onTap: () => onAppendText('0'),
        ),
        _KeyboardItem(
          ratio: 1,
          text: '确认',
          fontSize: 16,
          fontColor: MColor.xFFFFFFFF,
          bgColor: MColor.xFFFF918D,
          borderColor: MColor.xFFED726E,
          marginRight: 0,
          onTap: () => onConfirm(true),
        ),
      ], marginTop: 10),
    ];
  }

  Widget _buildKeyboardRow(List<_KeyboardItem> items, {required double marginTop}) {
    List<Widget> rowWidgets = [];
    
    for (var item in items) {
      rowWidgets.add(Expanded(
        flex: item.ratio,
        child: GestureDetector(
          onTap: item.onTap,
          child: Container(
            decoration: BoxDecoration(
              color: item.bgColor,
              borderRadius: BorderRadius.circular(10),
              border: Border.all(color: item.borderColor, width: 1),
              boxShadow: [
                BoxShadow(
                  offset: const Offset(0, 2),
                  blurRadius: 0,
                  color: item.borderColor,
                ),
              ],
            ),
            height: 36,
            padding: const EdgeInsets.symmetric(horizontal: 4),
            child: Center(
              child: item.widget ??
                  (item.icon != null
                      ? Image.asset(
                          item.icon!,
                          width: item.iconWidth,
                          height: item.iconHeight,
                          fit: BoxFit.fill,
                        )
                      : FittedBox(
                          fit: BoxFit.fill,
                          child: Text(
                            item.text!,
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                            style: TextStyle(
                              fontSize: item.fontSize!,
                              height: 1,
                              color: item.fontColor ?? item.borderColor,
                            ),
                          ),
                        )),
            ),
          ),
        ),
      ));
      
      if (item.marginRight > 0) {
        rowWidgets.add(SizedBox(width: item.marginRight));
      }
    }

    return Container(
      margin: EdgeInsets.only(top: marginTop),
      child: Row(children: rowWidgets),
    );
  }
}

class _KeyboardItem {
  final int ratio;
  final String? icon;
  final double? iconWidth;
  final double? iconHeight;
  final Color bgColor;
  final Color borderColor;
  final double marginRight;
  final String? text;
  final double? fontSize;
  final Color? fontColor;
  final Widget? widget;
  final VoidCallback? onTap;

  _KeyboardItem({
    required this.ratio,
    this.icon,
    this.iconWidth,
    this.iconHeight,
    required this.bgColor,
    required this.borderColor,
    required this.marginRight,
    this.text,
    this.fontSize,
    this.fontColor,
    this.widget,
    this.onTap,
  });
}
